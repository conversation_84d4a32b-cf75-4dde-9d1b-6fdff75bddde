name: release

on:
  push:
    branches:
      - master

permissions:
  contents: write
  packages: write
  
jobs:
  release:
    name: release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: master

      - name: Cache toolchains
        uses: actions/cache@v4
        with:
          path: ~/.bestsub/toolchains
          key: ${{ runner.os }}-toolchains-${{ hashFiles('go.mod') }}-${{ hashFiles('scripts/build.sh') }}

      - name: Setup Go
        uses: actions/setup-go@v5

      - name: Build
        run: bash scripts/build.sh release

      - name: Get latest tag
        id: tag
        run: |
          LATEST_TAG=$(git describe --tags --abbrev=0)
          echo "TAG_NAME=$LATEST_TAG" >> $GITHUB_OUTPUT

      - name: Upload Release
        uses: softprops/action-gh-release@v2
        with:
          files: build/archives/*
          prerelease: false
          tag_name: ${{ steps.tag.outputs.TAG_NAME }}

      - name: Docker meta (Debian)
        id: meta-debian
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/${{ github.repository }}
            ${{ secrets.DOCKERHUB_USERNAME }}/${{ github.event.repository.name }}
          tags: |
            type=ref,event=tag
            type=raw,value=latest

      - name: Docker meta (Alpine)
        id: meta-alpine
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/${{ github.repository }}
            ${{ secrets.DOCKERHUB_USERNAME }}/${{ github.event.repository.name }}
          tags: |
            type=ref,event=tag,suffix=-alpine
            type=raw,value=latest-alpine

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push (Alpine)
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./scripts/dockerfiles/Dockerfile.alpine
          push: true
          platforms: linux/amd64,linux/i386,linux/arm64,linux/arm/v7
          tags: ${{ steps.meta-alpine.outputs.tags }}
          labels: ${{ steps.meta-alpine.outputs.labels }}
          build-args: |
            TARGETPLATFORM

      - name: Build and push (Debian)
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./scripts/dockerfiles/Dockerfile.debian
          push: true
          platforms: linux/amd64,linux/i386,linux/arm64,linux/arm/v7
          tags: ${{ steps.meta-debian.outputs.tags }}
          labels: ${{ steps.meta-debian.outputs.labels }}
          build-args: |
            TARGETPLATFORM