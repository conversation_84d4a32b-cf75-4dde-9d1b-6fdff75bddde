FROM debian:bookworm

ARG TARGETPLATFORM
ENV TZ=Asia/Shanghai

RUN apt-get update && apt-get install -y ca-certificates tzdata && \
    ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    dpkg-reconfigure -f noninteractive tzdata && \
    rm -rf /var/cache/apt/*  && \
    mkdir -p /app

COPY build/docker/${TARGETPLATFORM}/bestsub /app/bestsub
COPY scripts/dockerfiles/entrypoint.sh /entrypoint.sh

RUN chmod +x /entrypoint.sh

CMD ["/entrypoint.sh"]