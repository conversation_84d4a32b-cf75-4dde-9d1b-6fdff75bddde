FROM alpine

ARG TARGETPLATFORM
ENV TZ=Asia/Shanghai

RUN apk add --no-cache alpine-conf ca-certificates && \
    /usr/sbin/setup-timezone -z Asia/Shanghai && \
    apk del alpine-conf && \
    rm -rf /var/cache/apk/* &&\
    mkdir -p /app

COPY build/docker/${TARGETPLATFORM}/bestsub /app/bestsub
COPY scripts/dockerfiles/entrypoint.sh /entrypoint.sh

RUN chmod +x /entrypoint.sh

CMD ["/entrypoint.sh"]