package middleware

import (
	"github.com/bestruirui/bestsub/internal/utils"
	"github.com/bestruirui/bestsub/internal/utils/log"
	"github.com/gin-gonic/gin"
)

// CORS中间件
// 在debug模式下允许所有跨域请求
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		if utils.IsDebug() {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
			c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Authorization, Content-Type, X-Requested-With")
			c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length")
			if c.Request.Method == "OPTIONS" {
				c.AbortWithStatus(204)
				return
			}
		} else {
			origin := c.Request.Header.Get("Origin")
			if origin != "" {
				log.Warnf("CORS %s: Origin=%s, Host=%s, Method=%s, Path=%s, IP=%s, UserAgent=%s",
					"BLOCKED", origin, c.Request.Host, c.Request.Method, c.Request.URL.Path,
					c.ClientIP(), c.GetHeader("User-Agent"))
			}
		}
		c.Next()
	}
}
