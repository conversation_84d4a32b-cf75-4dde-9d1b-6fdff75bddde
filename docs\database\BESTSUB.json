{"tables": [{"name": "save_template_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "sKZmp3T2AXcQ7-hdv4ZZr", "name": "save_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "-2HLjQA1x9tbl2QyR7iJn", "name": "template_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "HLXSYocs3qgu6pRnS5ltF", "x": 1681.2856304398024, "y": 1404.4519206648974}, {"name": "auth", "comment": "", "color": "#175e7a", "fields": [{"id": "PR6AEbQdKDcr8u-RptWzp", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "oXA5YyhQdOy3SalGstnp-", "name": "user_name", "type": "TEXT", "comment": "", "unique": true, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "8nc5DCFzRIYozbql00uH1", "name": "password", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "31N3mv0xQl6npaDqI5t54", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "P3tW6TLBVcJNppGXPbgrO", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "hF4ht2l-iDSxl_J0icMuw", "x": -341.1497354395268, "y": 1182.6620064368649}, {"name": "system_config", "comment": "", "color": "#175e7a", "fields": [{"id": "WJiVKLxG54Xa-mtapmhSF", "name": "key", "type": "TEXT", "comment": "", "unique": true, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "caknTgvcN1jVR0AAO1j9C", "name": "value", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}], "indices": [], "id": "ODgnCdTeSsLi2WzfdZBF4", "x": -616.2301785873767, "y": 1180.1868833664503}, {"name": "notify_templates", "comment": "", "color": "#175e7a", "fields": [{"id": "ww7kIN1-rImHMwfJLqQCK", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "CBhs4tdgpaknGY_wQT48t", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "AtXInptLa85MAOoau2C2S", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "Ncc3RSoEWIaii0Ctecocw", "name": "templates", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}], "indices": [], "id": "zFh2b4Pc-HSTaaAZoWlgg", "x": 2586.9680549510153, "y": 2410.1472984425736}, {"id": "kvQzd9fMTffNJSKOCAmgP", "name": "notify_config", "x": 2589.8908551373934, "y": 2737.6861055735085, "locked": false, "fields": [{"name": "id", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": true, "comment": "", "id": "QFQMfDLCEHG-NP1UzSbAt"}, {"id": "DUIi_UJ-ENVT4bZVOjTx3", "name": "enable", "type": "BOOLEAN", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "bwC_Tt0-OYffcpy7f8Jd-", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "lMrWBBROozt4l_FusFOsC", "name": "type", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}, {"id": "Nl82qU7Z2xVaexW1GKZes", "name": "config", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}], "comment": "", "indices": [], "color": "#175e7a"}, {"name": "tasks", "comment": "", "color": "#175e7a", "fields": [{"id": "aG1gtaUzePOIZY_jGU5LM", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "UPrxq7GYzZVVims6wbohl", "name": "enable", "type": "BOOLEAN", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "ufua1KzXVUz1-oGjakUrY", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "eDiXh1jdt2XP0kJZkg3w3", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "CYhtGWIkWtwzgXgTdK5iK", "name": "is_sys_task", "type": "BOOLEAN", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "6PWvuBnOjUP39y8RK_AMS", "name": "cron", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "t7SkS18SfdKeHLuJQOJZA", "name": "timeout", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "1QVltuCEAkMFV_kcVPX0P", "name": "type", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "WSWRlD7AQBYJeFSlH3RuC", "name": "log_level", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "WTa_5zJei10EArNOLeyfe", "name": "config", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "Ig_JteUCxB0KlTCbyBXrz", "name": "retry", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "z1O1kO8v7VOt_s0T6Bx7M", "name": "last_run_result", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "VYf6lwARan0XY1nv4_nFL", "name": "last_run_time", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": "", "size": "", "values": []}, {"id": "OTMY4CSsi1XysBi351pqe", "name": "last_run_duration", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": "", "size": "", "values": []}, {"id": "oqQaaJjXtAwizi35K8LAE", "name": "success_count", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "RgxA97k0RyFfi5lfTggXz", "name": "failed_count", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "ksYaFwfA_lWs067R5VXjg", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "g08rGZgl5kHk0tVDgRdCe", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "mE10alF0zdCyPAYnJXG4Z", "x": 1033.3218880583236, "y": 2597.351119360325}, {"name": "storage_configs", "comment": "", "color": "#175e7a", "fields": [{"id": "_OJwI_AVNrS9crFZVNrT0", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "MCBykSQsfiYS1QzABJD-F", "name": "enable", "type": "BOOLEAN", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "true", "check": ""}, {"id": "l3dy6IYMnJ8PRz9YCyk3J", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "Fb5xe6WY4MJhjYP7GmdVb", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "e1s0JvBcFkL2GLnMZyjGu", "name": "type", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "MnP6m7bnp6EDt6KbSI8n5", "name": "config", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "sI7VzcQbVJIaDHOB85tdH", "name": "test_result", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "B4dYZj8TvLsA5koK61lFJ", "name": "last_test", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "0wh_asXqcogp3SPZBdXr1", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "kOYZHpPNJM1K4LuF9RrNK", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "1z7iytESTi-Evutplwzss", "x": 3524.162422799508, "y": 1905.8159429186228}, {"name": "sub_output_templates", "comment": "", "color": "#175e7a", "fields": [{"id": "WkAgLU0si2A06gOFGijO5", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "yYLHdAiIxCQ3QOwUpRpNB", "name": "enable", "type": "BOOLEAN", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "true", "check": ""}, {"id": "Sg-MPbjSA8yFZcWzxFGov", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "xCMlUxWnKjUg2uFw99TRH", "name": "description", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "-IDWU3QRAqRmRAQHwcz6S", "name": "type", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "Qv_tcC9KKOTAHLqxV2qMb", "name": "template", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "7GejtCuntQ71XLuF4Q8oP", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "JUM1SeEF7lo9IyA0pyAMe", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "seN1SWFZ4MerDWOvCKNHH", "x": 1028.3485351204965, "y": 1230.6673451835607}, {"name": "sub_node_filter_rules", "comment": "", "color": "#175e7a", "fields": [{"id": "x7_P5nL3UvAnVNCNkHW-Q", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "SiJ9wrPK6cPJPRaOoB081", "name": "enable", "type": "BOOLEAN", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "HPyO-53Uxlc8bmm5WA7Hf", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "WgOHFO9dsMRezyrxdWhTb", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "uG0IolpKM_kW00GdCADci", "name": "field", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "Sboouw3emfZjDrRMRAU2M", "name": "operator", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "8E4PKIpJgebV2U2Mxieg2", "name": "value", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}, {"id": "rZ5_m0ZVpjLtoWgrqTntB", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "b_RvX-TGYaJrAbqGSEj-A", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "9ZaSMBRlzrEbI178i4lz0", "x": 1033.4542061881987, "y": 1703.9107160584592}, {"name": "subs", "comment": "", "color": "#175e7a", "fields": [{"id": "GcesojmBZpB085rr26Tm6", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "Kw61Wt27XBV5n0gDV8uJ7", "name": "enable", "type": "BOOLEAN", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "true", "check": ""}, {"id": "GHCRhcEYfok2T6q8GVD65", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "jLBRWFwErIt3myZN2Mwsl", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "lWwzTrB5SlZLKl6vIHiQd", "name": "url", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "tjmitxvORRqYxdsy1jDxs", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "eLiaMN_PTQmeW4vR0_vY7", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "Bd17dH_N1Y268sF9B-liE", "x": 1034.785151191579, "y": 2129.2587752901754}, {"name": "sub_save", "comment": "", "color": "#175e7a", "fields": [{"id": "J7N9Bjld-xmrg_QfmaG0M", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "oVKF5XNK8kKuuRJYfFrtT", "name": "enable", "type": "BOOLEAN", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "false", "check": ""}, {"id": "18dv-XQBdULp-lCCkYiGa", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": false, "default": "", "check": ""}, {"id": "_wtsDRy4r0OJUG5WYzC1M", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "vVUKqrCQsQPQm-dOKkxdz", "name": "rename", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "Qc7DfxhEeKf4yYizf7elM", "name": "file_name", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}, {"id": "jDUQVCBkAGynxn5oH6Hoa", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "YA2KNaUCCOdfTa7cOWEUX", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "WRXfMgG2BtWez4b7S0oTe", "x": 2570.9977751795877, "y": 1861.7948934416377}, {"name": "sub_share_links", "comment": "", "color": "#175e7a", "fields": [{"id": "fvjaDndetlTH9DZmEJg2s", "name": "id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": false, "primary": true, "default": "", "check": ""}, {"id": "5dFXi4j8CvGZdxQKnbE_v", "name": "enable", "type": "BOOLEAN", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "false", "check": ""}, {"id": "NFDgLwM6hG1zkYj7aA0Et", "name": "name", "type": "TEXT", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "x3EqB5YE_6P8s6W_7O--A", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "D1_m_U89hR79ldTDe344x", "name": "rename", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": ""}, {"id": "v1fql_Mc6fuVRptP1FXqA", "name": "access_count", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "0", "check": ""}, {"id": "FgrEISof7QDY9061yx1v0", "name": "max_access_count", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "EbiP7w4NBMCkeMwDrLLFv", "name": "token", "type": "TEXT", "comment": "", "unique": true, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "9dGj10BrtzWDUwOkt6W8Q", "name": "expires", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "", "check": ""}, {"id": "uKVaE2gpKvhk2H8QrcVSV", "name": "created_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}, {"id": "uzh3mVws0ImOgUKgcxxq-", "name": "updated_at", "type": "DATETIME", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": false, "default": "CURRENT_TIMESTAMP", "check": ""}], "indices": [], "id": "nEyTvxNl8C0HW1Lp8puZ_", "x": -607.758763368629, "y": 1841.222012849678}, {"name": "save_filter_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "kPr23Zwdnis4rVjdEd-DW", "name": "save_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "sEO_YKXQyZ5NkWfh0Ab-T", "name": "filter_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "xXaxs3w452drpmUViY9BC", "x": 1683.4462728087422, "y": 1668.6571595447354}, {"name": "save_storage_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "QtgjCmN2VxNShTe2DDgrx", "name": "save_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "EdNEJYlLKs81R0LEo6hBT", "name": "storage_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "Yrd6LM4652a5svwUArfOS", "x": 3017.5798361474217, "y": 1868.8161055385033}, {"name": "share_template_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "i4DnZNLN-BuwaQuPJC92l", "name": "share_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "Zy54AfAABU3TzjynYPPBe", "name": "template_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "uuHc3XJT5bD96R2RtsP4p", "x": 167.66167975263386, "y": 1600.7099947011416}, {"name": "share_filter_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "ipjPX93FtBo_PQ0fV7NnC", "name": "share_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "bsF7Utcvu9cm3JU-wTr7A", "name": "filter_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "1X4PRVqJ3piFiJYYL5eIL", "x": 161.26714827965088, "y": 1834.4563313633134}, {"name": "share_sub_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "l7ZLblEGaSd6wernuCsSh", "name": "share_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "UaqF9UaL6fXu38UC1RhYz", "name": "sub_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "PqD6Y4M9nZHxQ7gDBEsBN", "x": 153.35348935244792, "y": 2093.8792380039777}, {"name": "save_sub_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "HFwVqiAjhdmaOykIutSI7", "name": "save_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "UixVjglj-iX9TblJ6JdGU", "name": "sub_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "EHYC5dAyo-TL42-s-qXZm", "x": 1676.329017776373, "y": 2084.321825398757}, {"name": "sub_task_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "iq79Vr9mTWZ87bnlzZjDI", "name": "sub_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "mIV0EYyKqKCO6qRofIALE", "name": "task_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "jLQ-PvebJHUGDiEPxIQLm", "x": 156.09451259083173, "y": 2568.7046898910917}, {"name": "save_task_relations", "comment": "", "color": "#175e7a", "fields": [{"id": "R5gsIND2ZnGLWjMv2adPy", "name": "save_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}, {"id": "_Q3LFeJZoS80Jo6GWJMbG", "name": "task_id", "type": "INTEGER", "comment": "", "unique": false, "increment": false, "notNull": true, "primary": true, "default": "", "check": ""}], "indices": [], "id": "7j3rUKiyInHGjFuWvXgj5", "x": 1669.7564970007531, "y": 2349.797863677554}, {"id": "mijqsq48VtzJ44Xzces5A", "name": "notify_task_relations", "x": 1669.6854115756526, "y": 2809.539142250973, "locked": false, "fields": [{"id": "SJhdvtBL6aWEcgtUKPMzP", "name": "task_id", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"name": "notify_id", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "id": "yUIlcp7UAMI2AmFbWOs3K"}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "MQH-5FiiTStt65NLF_zSr", "name": "task_notify_template_relations", "x": 1676.3197898893834, "y": 2599.1680441650547, "locked": false, "fields": [{"name": "task_id", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "id": "TAbaCh_10wiW80FC7LbTP"}, {"id": "L67owH6O1CF6yeLF92N-V", "name": "notify_template_id", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "roCOQ7ump3CEzSfYBPF2C", "name": "migrations", "x": -875.8275682673755, "y": 1175.4274693173534, "locked": false, "fields": [{"id": "Zf0jXqrh7EGzrK-YTcD9y", "name": "date", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"name": "version", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "id": "fTssDY0UWuiYM8lCi-qbF", "size": ""}, {"id": "Yde2p87YyLTMpLv0QmfvD", "name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}, {"id": "sa0-U7ChrWjKZqEaP9vCP", "name": "applied_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a"}], "relationships": [{"name": "fk_save_template_relations_save_config_id_sub_save_configs", "startTableId": "HLXSYocs3qgu6pRnS5ltF", "endTableId": "WRXfMgG2BtWez4b7S0oTe", "endFieldId": "J7N9Bjld-xmrg_QfmaG0M", "startFieldId": "sKZmp3T2AXcQ7-hdv4ZZr", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 0}, {"name": "fk_save_template_relations_template_id_sub_output_templates", "startTableId": "HLXSYocs3qgu6pRnS5ltF", "endTableId": "seN1SWFZ4MerDWOvCKNHH", "endFieldId": "WkAgLU0si2A06gOFGijO5", "startFieldId": "-2HLjQA1x9tbl2QyR7iJn", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 1}, {"name": "fk_save_fitter_relations_save_config_id_sub_save_configs", "startTableId": "xXaxs3w452drpmUViY9BC", "endTableId": "WRXfMgG2BtWez4b7S0oTe", "endFieldId": "J7N9Bjld-xmrg_QfmaG0M", "startFieldId": "kPr23Zwdnis4rVjdEd-DW", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 2}, {"name": "fk_save_fitter_relations_fitter_id_sub_node_filter_rules", "startTableId": "xXaxs3w452drpmUViY9BC", "endTableId": "9ZaSMBRlzrEbI178i4lz0", "endFieldId": "x7_P5nL3UvAnVNCNkHW-Q", "startFieldId": "sEO_YKXQyZ5NkWfh0Ab-T", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 3}, {"name": "fk_save_storage_relations_save_config_id_sub_save_configs", "startTableId": "Yrd6LM4652a5svwUArfOS", "endTableId": "WRXfMgG2BtWez4b7S0oTe", "endFieldId": "J7N9Bjld-xmrg_QfmaG0M", "startFieldId": "QtgjCmN2VxNShTe2DDgrx", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 4}, {"name": "fk_save_storage_relations_storage_id_sub_storage_configs", "startTableId": "Yrd6LM4652a5svwUArfOS", "endTableId": "1z7iytESTi-Evutplwzss", "endFieldId": "_OJwI_AVNrS9crFZVNrT0", "startFieldId": "EdNEJYlLKs81R0LEo6hBT", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 5}, {"name": "fk_share_template_relations_share_id_sub_share_links", "startTableId": "uuHc3XJT5bD96R2RtsP4p", "endTableId": "nEyTvxNl8C0HW1Lp8puZ_", "endFieldId": "fvjaDndetlTH9DZmEJg2s", "startFieldId": "i4DnZNLN-BuwaQuPJC92l", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 6}, {"name": "fk_share_template_relations_template_id_sub_output_templates", "startTableId": "uuHc3XJT5bD96R2RtsP4p", "endTableId": "seN1SWFZ4MerDWOvCKNHH", "endFieldId": "WkAgLU0si2A06gOFGijO5", "startFieldId": "Zy54AfAABU3TzjynYPPBe", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 7}, {"name": "fk_share_fitter_relations_share_id_sub_share_links", "startTableId": "1X4PRVqJ3piFiJYYL5eIL", "endTableId": "nEyTvxNl8C0HW1Lp8puZ_", "endFieldId": "fvjaDndetlTH9DZmEJg2s", "startFieldId": "ipjPX93FtBo_PQ0fV7NnC", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 8}, {"name": "fk_share_fitter_relations_fitter_id_sub_node_filter_rules", "startTableId": "1X4PRVqJ3piFiJYYL5eIL", "endTableId": "9ZaSMBRlzrEbI178i4lz0", "endFieldId": "x7_P5nL3UvAnVNCNkHW-Q", "startFieldId": "bsF7Utcvu9cm3JU-wTr7A", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 9}, {"name": "fk_share_sub_relations_share_id_sub_share_links", "startTableId": "PqD6Y4M9nZHxQ7gDBEsBN", "endTableId": "nEyTvxNl8C0HW1Lp8puZ_", "endFieldId": "fvjaDndetlTH9DZmEJg2s", "startFieldId": "l7ZLblEGaSd6wernuCsSh", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 10}, {"name": "fk_share_sub_relations_sub_id_sub_links", "startTableId": "PqD6Y4M9nZHxQ7gDBEsBN", "endTableId": "Bd17dH_N1Y268sF9B-liE", "endFieldId": "GcesojmBZpB085rr26Tm6", "startFieldId": "UaqF9UaL6fXu38UC1RhYz", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 11}, {"name": "fk_save_sub_relations_sub_id_sub_links", "startTableId": "EHYC5dAyo-TL42-s-qXZm", "endTableId": "Bd17dH_N1Y268sF9B-liE", "endFieldId": "GcesojmBZpB085rr26Tm6", "startFieldId": "UixVjglj-iX9TblJ6JdGU", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 12}, {"name": "fk_save_sub_relations_save_config_id_sub_save_configs", "startTableId": "EHYC5dAyo-TL42-s-qXZm", "endTableId": "WRXfMgG2BtWez4b7S0oTe", "endFieldId": "J7N9Bjld-xmrg_QfmaG0M", "startFieldId": "HFwVqiAjhdmaOykIutSI7", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 13}, {"name": "fk_sub_task_relations_sub_id_sub_links", "startTableId": "jLQ-PvebJHUGDiEPxIQLm", "endTableId": "Bd17dH_N1Y268sF9B-liE", "endFieldId": "GcesojmBZpB085rr26Tm6", "startFieldId": "iq79Vr9mTWZ87bnlzZjDI", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "one_to_many", "id": 14}, {"name": "fk_sub_task_relations_task_id_tasks", "startTableId": "jLQ-PvebJHUGDiEPxIQLm", "endTableId": "mE10alF0zdCyPAYnJXG4Z", "endFieldId": "aG1gtaUzePOIZY_jGU5LM", "startFieldId": "mIV0EYyKqKCO6qRofIALE", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 15}, {"name": "fk_save_task_relations_task_id_tasks", "startTableId": "7j3rUKiyInHGjFuWvXgj5", "endTableId": "mE10alF0zdCyPAYnJXG4Z", "endFieldId": "aG1gtaUzePOIZY_jGU5LM", "startFieldId": "_Q3LFeJZoS80Jo6GWJMbG", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 16}, {"name": "fk_save_task_relations_save_id_sub_save_configs", "startTableId": "7j3rUKiyInHGjFuWvXgj5", "endTableId": "WRXfMgG2BtWez4b7S0oTe", "endFieldId": "J7N9Bjld-xmrg_QfmaG0M", "startFieldId": "R5gsIND2ZnGLWjMv2adPy", "updateConstraint": "No action", "deleteConstraint": "Cascade", "cardinality": "many_to_one", "id": 17}, {"startTableId": "mijqsq48VtzJ44Xzces5A", "startFieldId": "SJhdvtBL6aWEcgtUKPMzP", "endTableId": "mE10alF0zdCyPAYnJXG4Z", "endFieldId": "aG1gtaUzePOIZY_jGU5LM", "cardinality": "one_to_many", "updateConstraint": "No action", "deleteConstraint": "Cascade", "name": "fk_notify_task_task_id_tasks", "id": 18}, {"startTableId": "mijqsq48VtzJ44Xzces5A", "startFieldId": "yUIlcp7UAMI2AmFbWOs3K", "endTableId": "kvQzd9fMTffNJSKOCAmgP", "endFieldId": "QFQMfDLCEHG-NP1UzSbAt", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "Cascade", "name": "fk_notify_task_relations_notify_id_notify_config", "id": 19}, {"startTableId": "MQH-5FiiTStt65NLF_zSr", "startFieldId": "L67owH6O1CF6yeLF92N-V", "endTableId": "zFh2b4Pc-HSTaaAZoWlgg", "endFieldId": "ww7kIN1-rImHMwfJLqQCK", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "Cascade", "name": "fk_task_notify_template_relations_notify_template_id_notify_templates", "id": 20}, {"startTableId": "MQH-5FiiTStt65NLF_zSr", "startFieldId": "TAbaCh_10wiW80FC7LbTP", "endTableId": "mE10alF0zdCyPAYnJXG4Z", "endFieldId": "aG1gtaUzePOIZY_jGU5LM", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "Cascade", "name": "fk_task_notify_template_relations_task_id_tasks", "id": 21}], "notes": [], "subjectAreas": [], "database": "sqlite", "title": "BESTSUB"}