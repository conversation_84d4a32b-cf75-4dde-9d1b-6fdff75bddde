{"swagger": "2.0", "info": {"description": "BestSub -  API 文档\n\n这是 BestSub 的 API 文档\n\n## 认证\n大多数接口需要使用 JWT 令牌进行认证。\n认证时，请在 Authorization 头中包含 JWT 令牌：\n`Authorization: Bearer <your-jwt-token>`\n\n## 错误响应\n所有错误响应都遵循统一格式，包含 code、message 和 error 字段。\n\n## 成功响应\n所有成功响应都遵循统一格式，包含 code、message 和 data 字段。", "title": "BestSub API", "contact": {"name": "BestSub API 支持", "email": "<EMAIL>"}, "license": {"name": "GPL-3.0", "url": "https://opensource.org/license/gpl-3-0"}, "version": "1.0.0"}, "paths": {"/api/v1/auth/login": {"post": {"description": "用户登录接口，验证用户名和密码，返回JWT令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "用户登录", "parameters": [{"description": "登录请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.LoginRequest"}}], "responses": {"200": {"description": "登录成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.LoginResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "用户名或密码错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/auth/logout": {"post": {"security": [{"BearerAuth": []}], "description": "用户登出接口，使当前会话失效", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "用户登出", "responses": {"200": {"description": "登出成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/auth/refresh": {"post": {"description": "使用刷新令牌获取新的访问令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "刷新访问令牌", "parameters": [{"description": "刷新令牌请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.RefreshTokenRequest"}}], "responses": {"200": {"description": "刷新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.LoginResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "刷新令牌无效", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/auth/sessions": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前用户的所有活跃会话信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "获取用户会话列表", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.SessionListResponse"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/auth/sessions/{id}": {"delete": {"security": [{"BearerAuth": []}], "description": "删除指定ID的会话，使其失效", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "删除会话", "parameters": [{"type": "integer", "description": "会话ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "会话不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/auth/user": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前登录用户的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "获取用户信息", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.Data"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/auth/user/name": {"post": {"security": [{"BearerAuth": []}], "description": "修改当前用户的用户名", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "修改用户名", "parameters": [{"description": "修改用户名请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.UpdateUserInfoRequest"}}], "responses": {"200": {"description": "用户名修改成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "409": {"description": "用户名已存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/auth/user/password": {"post": {"security": [{"BearerAuth": []}], "description": "修改当前用户的密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "修改密码", "parameters": [{"description": "修改密码请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.ChangePasswordRequest"}}], "responses": {"200": {"description": "密码修改成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权或旧密码错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/check": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "获取检测列表", "parameters": [{"type": "integer", "description": "检测ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Response"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "创建单个检测", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "创建检测", "parameters": [{"description": "创建检测请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Request"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Response"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/check/type": {"get": {"security": [{"BearerAuth": []}], "description": "获取检测类型", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "获取检测类型", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/check/type/config": {"get": {"security": [{"BearerAuth": []}], "description": "获取检测类型对应的配置项", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "获取检测类型对应的配置项", "parameters": [{"type": "string", "description": "检测类型", "name": "type", "in": "query", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_core_check.Desc"}}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/check/{id}": {"put": {"security": [{"BearerAuth": []}], "description": "根据请求体中的ID更新检测信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "更新检测", "parameters": [{"type": "integer", "description": "检测ID", "name": "id", "in": "path", "required": true}, {"description": "更新检测请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Request"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Response"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "检测不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "根据ID删除单个检测", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "删除检测", "parameters": [{"type": "integer", "description": "检测ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "检测不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/check/{id}/run": {"post": {"security": [{"BearerAuth": []}], "description": "手动触发检测执行", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "手动运行检测", "parameters": [{"type": "integer", "description": "检测ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "运行成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "检测不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/check/{id}/stop": {"post": {"security": [{"BearerAuth": []}], "description": "停止正在运行的检测", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["检测"], "summary": "停止检测", "parameters": [{"type": "integer", "description": "检测ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "停止成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "检测不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/config/item": {"get": {"security": [{"BearerAuth": []}], "description": "获取系统所有配置项，支持按分组过滤和关键字搜索", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["配置"], "summary": "获取配置项", "parameters": [{"type": "string", "description": "分组名称", "name": "group", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_config.GroupAdvance"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "根据请求数据中的ID批量更新配置项的值和描述", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["配置"], "summary": "更新配置项", "parameters": [{"description": "更新配置项请求", "name": "request", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_config.UpdateAdvance"}}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_config.UpdateAdvance"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/log/content": {"get": {"security": [{"BearerAuth": []}], "description": "获取日志内容", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["日志"], "summary": "获取日志内容", "parameters": [{"type": "string", "description": "日志文件路径", "name": "path", "in": "query", "required": true}, {"type": "integer", "format": "int64", "description": "日志文件时间戳", "name": "timestamp", "in": "query", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"level": {"type": "string"}, "msg": {"type": "string"}, "time": {"type": "string"}}}}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "文件不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/log/list": {"get": {"security": [{"BearerAuth": []}], "description": "获取日志列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["日志"], "summary": "获取日志列表", "parameters": [{"type": "string", "description": "日志文件路径", "name": "path", "in": "query", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/notify": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "获取通知", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Data"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "根据请求体中的ID更新通知信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "更新通知", "parameters": [{"description": "更新通知请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Data"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Data"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "通知配置不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "创建单个通知", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "创建通知", "parameters": [{"description": "创建通知请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.CreateRequest"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Data"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "根据ID删除单个通知", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "删除通知", "parameters": [{"type": "integer", "description": "通知ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "通知不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/notify/channel": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "获取通知渠道", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/notify/channel/config": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "获取渠道配置", "parameters": [{"type": "string", "description": "渠道", "name": "channel", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_modules_notify.Desc"}}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/notify/template": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "获取通知模板", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Template"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "根据请求体中的ID更新通知模板信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "更新通知模板", "parameters": [{"description": "更新通知模板请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Template"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Template"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "通知模板不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/notify/test": {"post": {"security": [{"BearerAuth": []}], "description": "测试单个通知", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["通知"], "summary": "测试通知", "parameters": [{"description": "测试通知请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.CreateRequest"}}], "responses": {"200": {"description": "测试成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_notify.Data"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/share": {"get": {"security": [{"BearerAuth": []}], "description": "获取分享链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分享"], "summary": "获取分享链接", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_share.Response"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "创建分享链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分享"], "summary": "创建分享链接", "parameters": [{"description": "分享数据", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_share.Request"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_share.Response"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/share/{id}": {"put": {"security": [{"BearerAuth": []}], "description": "更新分享链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分享"], "summary": "更新分享链接", "parameters": [{"type": "string", "description": "分享ID", "name": "id", "in": "path", "required": true}, {"description": "分享数据", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_share.Request"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_share.Response"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "删除分享链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分享"], "summary": "删除分享链接", "parameters": [{"type": "string", "description": "分享ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/share/{token}": {"get": {"description": "获取订阅内容", "consumes": ["application/json"], "produces": ["text/plain"], "tags": ["分享"], "summary": "获取订阅内容", "parameters": [{"type": "string", "description": "分享token", "name": "token", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功，内容为yaml/plain格式", "schema": {"type": "string"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/storage": {"get": {"security": [{"BearerAuth": []}], "description": "获取存储", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["存储"], "summary": "获取存储", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_storage.Response"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "更新存储", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["存储"], "summary": "更新存储", "parameters": [{"description": "存储配置数据", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_storage.UpdateRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_storage.Response"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "创建存储", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["存储"], "summary": "创建存储", "parameters": [{"description": "存储配置数据", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_storage.CreateRequest"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_storage.Response"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/storage/channel": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["存储"], "summary": "获取存储渠道", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/storage/channel/config": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["存储"], "summary": "获取渠道配置", "parameters": [{"type": "string", "description": "渠道", "name": "channel", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_modules_storage.Desc"}}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/storage/{id}": {"delete": {"security": [{"BearerAuth": []}], "description": "删除存储", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["存储"], "summary": "删除存储", "parameters": [{"type": "string", "description": "存储ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/sub": {"get": {"security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅"], "summary": "获取订阅链接", "parameters": [{"type": "integer", "description": "链接ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Response"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "根据请求体中的ID更新订阅链接信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅"], "summary": "更新订阅链接", "parameters": [{"description": "更新订阅链接请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.UpdateRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Response"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "订阅链接不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "创建单个订阅链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅"], "summary": "创建订阅链接", "parameters": [{"description": "创建订阅链接请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.CreateRequest"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Response"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/sub/refresh/{id}": {"post": {"security": [{"BearerAuth": []}], "description": "根据ID手动刷新单个订阅", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅"], "summary": "手动刷新订阅", "parameters": [{"type": "integer", "description": "订阅链接ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "刷新成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Result"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "订阅链接不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/sub/{id}": {"delete": {"security": [{"BearerAuth": []}], "description": "根据ID删除单个订阅链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅"], "summary": "删除订阅链接", "parameters": [{"type": "integer", "description": "订阅链接ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "404": {"description": "订阅链接不存在", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/system/health": {"get": {"description": "检查服务健康状态，包括数据库连接状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统"], "summary": "健康检查", "responses": {"200": {"description": "服务正常", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_system.HealthResponse"}}}]}}, "503": {"description": "服务不可用", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/system/info": {"get": {"security": [{"BearerAuth": []}], "description": "获取程序运行相关信息，包括内存使用、运行时长、网络流量、CPU信息等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统"], "summary": "系统信息", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_system.Info"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}, "/api/v1/system/live": {"get": {"description": "检查服务是否存活（简单的ping检查）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统"], "summary": "存活检查", "responses": {"200": {"description": "服务存活", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}}}}}, "/api/v1/system/ready": {"get": {"description": "检查服务是否准备好接收请求", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统"], "summary": "就绪检查", "responses": {"200": {"description": "服务就绪", "schema": {"allOf": [{"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_system.HealthResponse"}}}]}}, "503": {"description": "服务未就绪", "schema": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct"}}}}}}, "definitions": {"github_com_bestruirui_bestsub_internal_core_check.Desc": {"type": "object", "properties": {"default": {"type": "string"}, "desc": {"type": "string"}, "name": {"type": "string"}, "options": {"type": "string"}, "require": {"type": "boolean"}, "type": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_auth.ChangePasswordRequest": {"type": "object", "required": ["new_password", "old_password", "username"], "properties": {"new_password": {"type": "string", "example": "new_password"}, "old_password": {"type": "string", "example": "old_password"}, "username": {"type": "string", "example": "admin"}}}, "github_com_bestruirui_bestsub_internal_models_auth.Data": {"type": "object", "properties": {"username": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_auth.LoginRequest": {"type": "object", "required": ["password", "username"], "properties": {"password": {"type": "string", "example": "admin"}, "username": {"type": "string", "example": "admin"}}}, "github_com_bestruirui_bestsub_internal_models_auth.LoginResponse": {"type": "object", "properties": {"access_expires_at": {"type": "string", "example": "2024-01-01T12:00:00Z"}, "access_token": {"type": "string", "example": "access_token_string"}, "refresh_expires_at": {"type": "string", "example": "2024-01-01T12:00:00Z"}, "refresh_token": {"type": "string", "example": "refresh_token_string"}}}, "github_com_bestruirui_bestsub_internal_models_auth.RefreshTokenRequest": {"type": "object", "required": ["refresh_token"], "properties": {"refresh_token": {"type": "string", "example": "refresh_token_string"}}}, "github_com_bestruirui_bestsub_internal_models_auth.SessionListResponse": {"type": "object", "properties": {"sessions": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_auth.SessionResponse"}}, "total": {"type": "integer"}}}, "github_com_bestruirui_bestsub_internal_models_auth.SessionResponse": {"type": "object", "properties": {"client_ip": {"type": "string"}, "created_at": {"type": "string"}, "expires_at": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "last_access_at": {"type": "string"}, "user_agent": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_auth.UpdateUserInfoRequest": {"type": "object", "required": ["username"], "properties": {"username": {"type": "string", "example": "admin"}}}, "github_com_bestruirui_bestsub_internal_models_check.Request": {"type": "object", "properties": {"config": {}, "enable": {"type": "boolean"}, "name": {"type": "string", "example": "测试检测任务"}, "task": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Task"}}}, "github_com_bestruirui_bestsub_internal_models_check.Response": {"type": "object", "properties": {"config": {}, "enable": {"type": "boolean"}, "id": {"type": "integer"}, "name": {"type": "string"}, "result": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Result"}, "status": {"type": "string"}, "task": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_check.Task"}}}, "github_com_bestruirui_bestsub_internal_models_check.Result": {"type": "object", "properties": {"duration": {"type": "integer"}, "extra": {}, "last_run": {"type": "string"}, "msg": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_check.Task": {"type": "object", "properties": {"cron_expr": {"type": "string", "example": "0 0 * * *"}, "log_level": {"type": "string", "example": "info"}, "log_write_file": {"type": "boolean", "example": true}, "notify": {"type": "boolean", "example": true}, "notify_channel": {"type": "integer", "example": 1}, "timeout": {"type": "integer", "example": 60}, "type": {"type": "string", "example": "test"}}}, "github_com_bestruirui_bestsub_internal_models_config.Advance": {"type": "object", "properties": {"description": {"type": "string"}, "key": {"type": "string"}, "options": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_config.GroupAdvance": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_config.Advance"}}, "description": {"type": "string"}, "group_name": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_config.UpdateAdvance": {"type": "object", "properties": {"key": {"type": "string", "example": "proxy.enabled"}, "value": {"type": "string", "example": "true"}}}, "github_com_bestruirui_bestsub_internal_models_node.Filter": {"type": "object", "properties": {"aliveStatus": {"description": "存活状态位字段筛选（0表示不筛选，其他值表示必须匹配的位）", "type": "integer", "format": "int32"}, "country": {"description": "ISO 3166数字国家代码（0表示不筛选，>0表示具体国家，最大65535）", "type": "integer", "format": "int32"}, "delayLessThan": {"description": "延迟小于指定值（0表示不筛选，>0表示具体值，毫秒，最大65535ms）", "type": "integer", "format": "int32"}, "riskLessThan": {"description": "风险等级小于指定值（0表示不筛选，>0表示具体值，百分比，最大255）", "type": "integer", "format": "int32"}, "speedDownMore": {"description": "下载速度大于指定值（0表示不筛选，>0表示具体值，KB/s，最大65535KB/s）", "type": "integer", "format": "int32"}, "speedUpMore": {"description": "上传速度大于指定值（0表示不筛选，>0表示具体值，KB/s，最大65535KB/s）", "type": "integer", "format": "int32"}}}, "github_com_bestruirui_bestsub_internal_models_notify.CreateRequest": {"type": "object", "required": ["config", "name", "type"], "properties": {"config": {"description": "通知配置", "type": "string", "example": "{\"server\":\"smtp.example.com\",\"port\":587,\"username\":\"<EMAIL>\",\"password\":\"test\",\"from\":\"<EMAIL>\",\"tls\":true,\"to\":\"<EMAIL>\"}"}, "name": {"description": "通知名称", "type": "string"}, "type": {"description": "通知类型", "type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_notify.Data": {"type": "object", "properties": {"config": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_notify.Template": {"type": "object", "properties": {"template": {"description": "模板内容", "type": "string"}, "type": {"description": "模板类型", "type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_share.Config": {"type": "object", "properties": {"expires": {"type": "integer", "example": 1722336000}, "filter": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_node.Filter"}, "max_access_count": {"type": "integer"}, "sub_id": {"type": "array", "items": {"type": "integer"}}, "template": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_share.Request": {"type": "object", "properties": {"config": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_share.Config"}, "enable": {"type": "boolean"}, "name": {"type": "string"}, "token": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_share.Response": {"type": "object", "properties": {"access_count": {"type": "integer"}, "config": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_share.Config"}, "enable": {"type": "boolean"}, "id": {"type": "integer"}, "name": {"type": "string"}, "token": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_storage.CreateRequest": {"type": "object", "properties": {"config": {}, "name": {"type": "string", "example": "webdav"}, "type": {"type": "string", "example": "webdav"}}}, "github_com_bestruirui_bestsub_internal_models_storage.Response": {"type": "object", "properties": {"config": {}, "id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_storage.UpdateRequest": {"type": "object", "properties": {"config": {}, "id": {"type": "integer"}, "name": {"type": "string", "example": "webdav"}, "type": {"type": "string", "example": "webdav"}}}, "github_com_bestruirui_bestsub_internal_models_sub.Config": {"type": "object", "properties": {"proxy": {"type": "boolean", "example": false}, "timeout": {"type": "integer", "example": 10}, "url": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_sub.CreateRequest": {"type": "object", "properties": {"config": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Config"}, "cron_expr": {"type": "string", "example": "0 0 * * *"}, "enable": {"type": "boolean"}, "name": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_sub.NodeInfo": {"type": "object", "properties": {"alive_count": {"type": "integer"}, "delay": {"type": "integer"}, "raw_count": {"type": "integer"}, "risk": {"type": "integer"}, "speed_down": {"type": "integer"}, "speed_up": {"type": "integer"}}}, "github_com_bestruirui_bestsub_internal_models_sub.Response": {"type": "object", "properties": {"config": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Config"}, "created_at": {"type": "string"}, "cron_expr": {"type": "string"}, "enable": {"type": "boolean"}, "id": {"type": "integer"}, "name": {"type": "string"}, "node_info": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.NodeInfo"}, "result": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Result"}, "status": {"type": "string"}, "updated_at": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_sub.Result": {"type": "object", "properties": {"count": {"type": "integer"}, "duration": {"type": "integer"}, "fail": {"type": "integer"}, "last_run": {"type": "string"}, "msg": {"type": "string"}, "raw_count": {"type": "integer"}, "success": {"type": "integer"}}}, "github_com_bestruirui_bestsub_internal_models_sub.UpdateRequest": {"type": "object", "properties": {"config": {"$ref": "#/definitions/github_com_bestruirui_bestsub_internal_models_sub.Config"}, "cron_expr": {"type": "string", "example": "0 0 * * *"}, "enable": {"type": "boolean"}, "id": {"type": "integer"}, "name": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_models_system.HealthResponse": {"type": "object", "properties": {"database": {"description": "数据库状态", "type": "string", "example": "connected"}, "status": {"description": "服务状态", "type": "string", "example": "ok"}, "timestamp": {"description": "检查时间", "type": "string", "example": "2024-01-01T12:00:00"}, "version": {"description": "版本信息", "type": "string", "example": "1.0.0"}}}, "github_com_bestruirui_bestsub_internal_models_system.Info": {"type": "object", "properties": {"cpu_percent": {"description": "CPU 占用率", "type": "number"}, "download_bytes": {"description": "下载流量 (bytes)", "type": "integer"}, "memory_used": {"description": "已使用内存 (bytes)", "type": "integer"}, "start_time": {"description": "启动时间", "type": "string"}, "upload_bytes": {"description": "上传流量 (bytes)", "type": "integer"}}}, "github_com_bestruirui_bestsub_internal_modules_notify.Desc": {"type": "object", "properties": {"default": {"type": "string"}, "desc": {"type": "string"}, "name": {"type": "string"}, "options": {"type": "string"}, "require": {"type": "boolean"}, "type": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_modules_storage.Desc": {"type": "object", "properties": {"default": {"type": "string"}, "desc": {"type": "string"}, "name": {"type": "string"}, "options": {"type": "string"}, "require": {"type": "boolean"}, "type": {"type": "string"}}}, "github_com_bestruirui_bestsub_internal_server_resp.ErrorStruct": {"type": "object", "properties": {"code": {"description": "状态码", "type": "integer", "example": 400}, "error": {"description": "错误详情", "type": "string", "example": "Invalid request format"}, "message": {"description": "响应消息", "type": "string", "example": "error"}}}, "github_com_bestruirui_bestsub_internal_server_resp.SuccessStruct": {"type": "object", "properties": {"code": {"description": "状态码", "type": "integer", "example": 200}, "data": {"description": "响应数据"}, "message": {"description": "响应消息", "type": "string", "example": "success"}}}}, "securityDefinitions": {"BearerAuth": {"description": "类型为 \"Bearer\"，后跟空格和 JWT 令牌。", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}