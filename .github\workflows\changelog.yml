name: changelog

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  changelog:
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.MY_TOKEN }}

      - run: npx changelogithub
        env:
          GITHUB_TOKEN: ${{secrets.MY_TOKEN}}

      - name: Merge main to master branch
        run: |
          git config --global user.name 'GitHub Actions'
          git config --global user.email '<EMAIL>'
          if ! git show-ref --verify --quiet refs/heads/master; then
            git checkout -b master
          else
            git checkout master
          fi
          git fetch origin main
          git merge origin/main
          git remote set-url origin https://x-access-token:${GITHUB_TOKEN}@github.com/${{ github.repository }}
          git push origin master --force
        env:
          GITHUB_TOKEN: ${{secrets.MY_TOKEN}}