# 数据库设计文档

## 概述
本文档介绍项目的数据库设计方案及相关工具使用说明。

## 设计图查看方法
1. 下载本目录中的设计文件：[BESTSUB.json](./BESTSUB.json)
2. 访问 [DrawDB](https://www.drawdb.app/) 在线工具
3. 将下载的JSON文件导入到DrawDB中
4. 即可查看完整的数据库设计图及表结构关系

## 推荐工具
- **数据库设计工具**：[DrawDB](https://github.com/drawdb-io/drawdb) - 免费且功能强大的数据库设计工具
- **SQLite可视化工具**：[sqlite3-editor](https://github.com/yy0931/sqlite3-editor) - 方便直观的SQLite数据库查看和编辑工具

## 使用说明
在开发过程中，请参照数据库设计图进行数据库操作，确保数据结构的一致性和完整性。如需修改数据库设计，请更新设计文件并同步到项目中。